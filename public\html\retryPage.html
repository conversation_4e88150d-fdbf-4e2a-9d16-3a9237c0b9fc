<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
    #failedToLoadPage {
        display: flex;
        flex-direction: column;
        position: fixed;
        width: 100vw;
        height: 100vh;
        align-items: center;
        justify-content: center;
    }

    #failedToLoadPage>h2 {
        color: #de6834;
    }

    #failedToLoadPage>p>a {
        color: #de6834;
    }
</style>
<script>
    async function retryLoad() {
        try {
            await window.api.retryPageReload();
        } catch (error) {
            console.error(error);
        }
    }
</script>

<body>
    <div id="failedToLoadPage">
        <h2 class="">Failed to load the app.</h2>
        <p>Please check your internet connection and <a href="#" onclick="retryLoad()">reload</a>.</p>
    </div>
</body>

</html>