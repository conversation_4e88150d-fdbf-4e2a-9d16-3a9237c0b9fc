{"productName": "CQ TestApp", "appId": "com.codequotient.test", "files": ["!.env", "!electron-builder.chitkara.env", "!electron-builder.env", "!electron-builder.json", "!preBuild.js", "!template.json", "!macOsCertification", "!monitorDetect.c"], "publish": {"provider": "spaces", "name": "cq-cdn", "region": "blr1", "path": "/TestApp", "acl": "public-read"}, "protocols": {"name": "CQ TestApp", "schemes": ["test-codequotient"]}, "extraResources": ["./extra/**"], "asar": true, "asarUnpack": ["node_modules/ffmpeg-static/bin/${os}/${arch}/ffmpeg", "node_modules/ffmpeg-static/index.js", "node_modules/ffmpeg-static/package.json"], "compression": "normal", "removePackageScripts": true, "artifactName": "${name}-${version}.${ext}", "win": {"target": "nsis"}, "mac": {"hardenedRuntime": true, "notarize": {"teamId": "59B2MAAW77"}, "x64ArchFiles": "*", "entitlements": "entitlements.mac.plist", "extendInfo": {"NSMicrophoneUsageDescription": "Please give us access to your microphone", "NSCameraUsageDescription": "Please give us access to your camera", "com.apple.security.device.microphon": true, "com.apple.security.device.audio-input": true, "com.apple.security.device.camera": true, "com.apple.security.cs.allow-jit": true, "CFBundleURLTypes": true, "com.apple.security.cs.allow-dyld-environment-variables": true, "com.apple.security.cs.allow-unsigned-executable-memory": true}}, "linux": {"category": "Utility", "desktop": {"StartupNotify": true, "Encoding": "UTF-8", "MimeType": "x-scheme-handler/test-codequotient"}, "target": ["deb"]}, "nsis": {"deleteAppDataOnUninstall": true, "runAfterFinish": true}}