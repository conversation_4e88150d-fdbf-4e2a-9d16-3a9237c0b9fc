<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TestPad</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Hind:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4bw+/aepP/YC94hEpVNVgiZdgIC5+VKNBQNGCHeKRQN+PtmoHDEXuppvnDJzQIu9" crossorigin="anonymous">
    <link rel="stylesheet" href="../css/index.css">
    <style>
        .exit-alert{
            text-align: center;
            color: #de6834;
            background: #ffe0b269;
            padding: 0.5em 2em;
            border-radius: 15px;
            color: #f57c00cf;
            width: max-content;
            margin: auto;
            margin-top: 48px;
        }
    </style>
</head>
<style>
    .hide {
        display: none;
    }
    #loading-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 10;
        background-color: rgba(0, 0, 0, 0.2);
    }
    #alert-message{
        position: fixed;
        transition: top 0.3s ease-in-out;
        top: -80px;
        left: 50%;
        transform: translate(-50%,0);
    }
    #loader {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        border: 1px solid gray;
        border-top: 1px white;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { 
            transform: rotate(0deg); 
        }
        100% {
            transform: rotate(360deg); 
        }
    }


    .logo-container img{
        height: 80px;
    }
</style>
<body>
    <div id="alert-message" class="alert" role="alert">
        A simple primary alert—check it out!
    </div>
    <div id="loading-background" class="hide"></div>
    
    <div id="loader" class="hide"></div>
    <div class="logo-container">
        <img src="../images/chitkara_logo_primary.png" alt="logo">
    </div>
    <div class="form-container">
        <!-- <form id="link-form">
        </form> -->
        <div id="link-form">
            <div class="mb-3">
              <label for="link-input" class="form-label">Enter test/invite link</label>
              <input type="text" class="form-control" id="link-input">
    
            </div>
            <button id="goToBtn" onclick="startTest()" class="btn btn-primary">Go</button>

            <div>
                <h4 class="exit-alert" id="closeInfo"></h4>
        </div>
    </div>

    <div class="footer-container">
        <p>Powered by Chitkara</p>
        <p>Version: <span id="app-version"></span></p>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded',() => {
            const version = window.api.getAppVersion();
            const howToClose = window.api.getCloseInstructions();
            document.getElementById('closeInfo').innerText = howToClose;
            document.getElementById('app-version').innerText = version;
        })
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js" integrity="sha384-HwwvtgBNo3bZJJLYd8oVXjrBZt8cqVSpeBNS5n7C8IVInixGAoxmnlMuBnhbgrkm" crossorigin="anonymous"></script>
    <script>
        valid_urls = [
            'http://localhost',
            'https://assess.testpad.chitkara.edu.in',
            'https://exam.testpad.chitkara.edu.in',
            'https://assess.testpad.chitkarauniversity.edu.in',
            'https://exam.testpad.chitkarauniversity.edu.in',
            'https://cqtestga.com',
            'https://tests.cqtestga.com',
        ]
    </script>
</body>
<script src="../js/index.js"></script>
</html>
