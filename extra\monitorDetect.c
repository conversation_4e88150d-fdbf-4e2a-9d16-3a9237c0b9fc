#include <windows.h>
#include <stdio.h>

BOOL CALLBACK MonitorEnumProc(HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) {
    int *count = (int *)dwData;
    (*count)++;
    return TRUE;
}

int main() {
    int monitorCount = 0;
    if (EnumDisplayMonitors(NULL, NULL, MonitorEnumProc, (LPARAM)&monitorCount)) {
        printf("Number of monitors: %d\n", monitorCount);
        if (monitorCount > 1) {
            exit(20);
        }
    } else {
        printf("Failed to enumerate monitors.\n");
        exit(2);
    }
    exit(0);
}
