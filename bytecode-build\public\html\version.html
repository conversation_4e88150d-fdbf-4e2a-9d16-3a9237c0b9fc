<!DOCTYPE html>
<html>
<head>
  <title>Electron UpdateExample</title>
</head>
<style>
  *{
    padding: 0px;
    margin: 0px;
    overflow: hidden;
  }
  html{
    background-color: transparent;
    width: 100%;
    height: 100%;
  }
  body{
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background-color: #222020;
  }
  img{
    display: block;
    height: 100px;
    width: auto;
    pointer-events: none;
  }
  #title {
    color: whitesmoke;
    padding: 10px 0px;
    transition: all 0.5s ease-in-out;
  }
  progress{
    accent-color: orange;
  }
  #update {
    visibility: hidden;
    
  }
</style>
<body>
  <div><img src="../images/logo.png"></div>
  <p id="title">Checking For Update</p>
  <progress id="update", value="0", max="100"></progress>
<script>
// Listen for messages
const {ipcRenderer} = require('electron');
ipcRenderer.on('message', function(event, text) {
  var container = document.getElementById('messages');
  var message = document.createElement('div');
  message.innerHTML = text;
  container.appendChild(message);
})
ipcRenderer.on('update-progress', (ev, percentage, speed, total) => {
  document.getElementById('title').innerText = 'Updating'
  /** @type {HTMLProgressElement} */
  const pBar = document.getElementById('update');
  pBar.style.visibility = "visible";
  pBar.value = percentage;
})
ipcRenderer.on('check-version', (url) => {
  alert('Checking version');
  fetch()
})
</script>
</body>
</html>