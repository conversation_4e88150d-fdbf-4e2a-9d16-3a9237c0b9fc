import sql from 'sqlite3';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

const filePath = '/Library/Application Support/com.apple.TCC/TCC.db';
// const fileData = fs.readFileSync(filePath).toString();
// console.log(fileData);

let db
try {
    const result = fs.readFileSync(filePath);
    console.log(result);
    if (result) {
        db = await new Promise((resolve, reject) => {
            const db = new sql.Database(filePath);
            db.on('open', () => {
                resolve(db);
            })
            db.on('error', () => {
                reject(`Dont have Access To DB`);
            })
        })
    }
} catch (error) {
    console.log(error);
    if ((error.message ?? error).includes('operation not permitted')) {
        process.exitCode = 23;
        process.exit(23);
    }
    process.exit(2);
}

const serviceToCheck = 'kTCCServiceScreenCapture';

function applicationNameFromBundleId(bundleId) {
    try {
        const command = `mdfind kMDItemCFBundleIdentifier = ${bundleId}`;
        const result = execSync(command);
        return result.toString().split('/').pop().replace('.app','').split(' ').join('\\ ');
    } catch (error) {
        return null;
    }
}

function killProcess(applicationName) {
    try {
        const command = `killall ${applicationName}; killall ${applicationName}`;
        const result = execSync(command);
        return result;
    } catch (error) {
        console.log(error);
    }
}
function tccUtilReset() {
    try {
        const command = `tccutil reset ScreenCapture`;
        return execSync(command).toString();
    } catch (error) {
        console.log(error);
    }
}

const bundleIdsFound = (await new Promise((resolve, reject ) => {
    try {
        db.all(`SELECT * from access;`, (error, rows) => {
            if (error) throw new Error(error);
            const servicesToStop = new Set();
            try {
                rows.forEach((row) => {
                    if (row.service !== serviceToCheck) {
                        return;
                    }
                    servicesToStop.add(row.client);
                    console.log('PID ',row.pid);
                })
                resolve(Array.from(servicesToStop));
            } catch (error) {
                console.log(error);
                reject(error?.message ?? error);
            }
        })
    } catch (error) {
        console.log(error)
        reject(error?.message ?? error);
    }
})).map((singleBundleId) => {
    console.log("Bundle ID: ", singleBundleId);
    const result = applicationNameFromBundleId(singleBundleId);
    console.log(result);
    console.log("\n\n");
    return result;
}).forEach((appliation) => killProcess(appliation));

tccUtilReset();