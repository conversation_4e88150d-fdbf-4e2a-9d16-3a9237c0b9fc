{"name": "testpad", "version": "0.4.12", "main": "bytecode-build/index.js", "repository": "**************:CQ-Dev-Team/test_electron.git", "author": {"email": "<EMAIL>", "name": "Chitkara"}, "license": "MIT", "scripts": {"dev": "electron .", "pub": " electron-builder --publish always", "build:win": "rimraf ./dist && node preBuild --NODE_ENV testing --CHITKARA true &&  electron-builder --win", "build:linux": "rimraf ./dist && node preBuild --NODE_ENV testing && electron-builder --linux", "build:mac": "rimraf ./dist && node preBuild --NODE_ENV testing --CHITKARA true && electron-builder --mac", "build:local": "rimraf ./dis && node preBuild --NODE_ENV local && electron-builder", "build:local:test": "rimraf ./dis && electron-builder ", "postinstall": "npx electron-rebuild ."}, "devDependencies": {"@electron/notarize": "^2.3.0", "electron": "^31.0.0", "electron-builder": "24.12.0", "remote": "^0.2.6", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.5.1", "bytenode": "^1.5.7", "check-disk-space": "^3.4.0", "dayjs": "^1.11.13", "dotenv": "^16.3.1", "electron-localshortcut": "^3.2.1", "electron-log": "^4.4.8", "electron-updater": "^6.1.4", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.1", "sequelize": "^6.37.5", "sqlite3": "5.1.6", "umzug": "^3.8.2", "v8-compile-cache": "^2.4.0"}}