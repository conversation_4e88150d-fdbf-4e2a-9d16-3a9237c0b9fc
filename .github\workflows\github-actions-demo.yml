name: GitHub Actions Demo
run-name: Building Apps
on: [push]
jobs:
  Build-Windows-App-Testing:
    if: github.ref == 'refs/heads/dev-testing' ||  github.ref == 'refs/heads/dev-testing-window'
    runs-on: windows-latest
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event  on  ${{ github.ref }}."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitH<PERSON>!"
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - name: Setup C
        uses: rlalik/setup-cpp-compiler@master
        with:
          compiler: gcc-13.1.0
      - run: node preBuild.js --NODE_ENV testing
      - run: |
          yarn pub --win
  
  Build-Windows-App-Testing-Chitkara:
    if: github.ref == 'refs/heads/dev-testing' || github.ref == 'refs/heads/dev-testing-chitkara' || github.ref == 'refs/heads/dev-testing-window'
    runs-on: windows-latest
    steps:
      - run: echo "Running Chitkara Build"
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event  on  ${{ github.ref }}."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - name: Setup C
        uses: rlalik/setup-cpp-compiler@master
        with:
          compiler: gcc-13.1.0
      - run: node preBuild.js --NODE_ENV testing --CHITKARA true
      - run: |
          yarn pub --win

  Build-Windows-App-Production:
    runs-on: windows-latest
    if: github.ref == 'refs/heads/production' || github.ref == 'refs/heads/production-windows' || github.ref == 'refs/heads/production-cq-windows'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - name: Setup C
        uses: rlalik/setup-cpp-compiler@master
        with:
          compiler: gcc-13.1.0
      - run: node preBuild.js --NODE_ENV production
      - run: |
          yarn pub --win

  
  Build-Windows-App-Production-Chitkara:
    if: github.ref == 'refs/heads/production' || github.ref == 'refs/heads/production-windows' || github.ref == 'refs/heads/production-chitkara' || github.ref == 'refs/heads/production-chitkara-window'
    runs-on: windows-latest
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event  on  ${{ github.ref }}."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - name: Setup C
        uses: rlalik/setup-cpp-compiler@master
        with:
          compiler: gcc-13.1.0
      - run: node preBuild.js --NODE_ENV production --CHITKARA true
      - run: |
          yarn pub --win
  
  Build-Linux-App-Testing:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev-testing'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - run: node preBuild.js --NODE_ENV testing
      - run: |
          export DEBUG=*
          yarn pub --linux

  Build-Linux-App-Chitkara-Testing:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/dev-testing' || github.ref == 'refs/heads/dev-testing-chitkara'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - run: node preBuild.js --NODE_ENV testing --CHITKARA true
      - run: |
          export DEBUG=*
          yarn pub --linux
  
  Build-Linux-App-Production:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - run: node preBuild.js --NODE_ENV production
      - run: |
          export DEBUG=*
          yarn pub --linux


  Build-Linux-App-Chitkara-Production:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/production' || github.ref == 'refs/heads/production-chitkara' || github.ref == 'refs/heads/production-chitkara-window'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - run: node preBuild.js --NODE_ENV production --CHITKARA true
      - run: |
          export DEBUG=*
          yarn pub --linux
  
  Build-Mac-App-Testing:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/dev-testing' || github.ref == 'refs/heads/dev-testing-mac'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: brew install python-setuptools
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0
      - run: yarn
      - run: npx electron-rebuild .
      - run: node preBuild.js --NODE_ENV testing
      - name: Setup KeyChain
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
          # default again user login keychain
          security list-keychains -d user -s login.keychain

          # Create temp keychain
          security create-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Append temp keychain to the user domain
          security list-keychains -d user -s "$MY_KEYCHAIN" $(security list-keychains -d user | sed s/\"//g)

          # Remove relock timeout
          security set-keychain-settings "$MY_KEYCHAIN"

          # Unlock keychain
          security unlock-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Add certificate to keychain
          security import $CERT -k "$MY_KEYCHAIN" -P "$CERT_PASSWORD" -A -T "/usr/bin/codesign" -T "/usr/bin/productsign"

          # Enable codesigning from a non user interactive shell
          security set-key-partition-list -S apple-tool:,apple:, -s -k $MY_KEYCHAIN_PASSWORD -D "${IDENTITY_CERTIFICATE}" -t private $MY_KEYCHAIN

      - name: Deploy
        env:
          CSC_KEY_PASSWORD: admin@cq
          CSC_LINK: ./macOsCertification/certificate.p12
          APPLE_APP_SPECIFIC_PASSWORD: dlen-aztg-zajf-wwqu
          APPLE_ID: <EMAIL>
          APPLE_TEAM_ID: 59B2MAAW77
        run: |
          export DEBUG=*
          yarn pub --mac --universal
      - name: Clean up
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
            # Delete temporary keychain
            security delete-keychain "$MY_KEYCHAIN"

            # default again user login keychain
            security list-keychains -d user -s login.keychain


  Build-Mac-App-Chitkara-Testing:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/dev-testing' || github.ref == 'refs/heads/dev-testing-mac' || github.ref == 'refs/heads/back-dev-testing-mac'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: brew install python-setuptools
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0
      - run: yarn
      - run: npx electron-rebuild .
      - run: node preBuild.js --NODE_ENV testing --CHITKARA true
      - name: Setup KeyChain
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
          # default again user login keychain
          security list-keychains -d user -s login.keychain

          # Create temp keychain
          security create-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Append temp keychain to the user domain
          security list-keychains -d user -s "$MY_KEYCHAIN" $(security list-keychains -d user | sed s/\"//g)

          # Remove relock timeout
          security set-keychain-settings "$MY_KEYCHAIN"

          # Unlock keychain
          security unlock-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Add certificate to keychain
          security import $CERT -k "$MY_KEYCHAIN" -P "$CERT_PASSWORD" -A -T "/usr/bin/codesign" -T "/usr/bin/productsign"

          # Enable codesigning from a non user interactive shell
          security set-key-partition-list -S apple-tool:,apple:, -s -k $MY_KEYCHAIN_PASSWORD -D "${IDENTITY_CERTIFICATE}" -t private $MY_KEYCHAIN

      - name: Deploy
        env:
          CSC_KEY_PASSWORD: admin@cq
          CSC_LINK: ./macOsCertification/certificate.p12
          APPLE_APP_SPECIFIC_PASSWORD: dlen-aztg-zajf-wwqu
          APPLE_ID: <EMAIL>
          APPLE_TEAM_ID: 59B2MAAW77
        run: |
          export DEBUG=*
          yarn pub --mac --universal
      - name: Clean up
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
            # Delete temporary keychain
            security delete-keychain "$MY_KEYCHAIN"

            # default again user login keychain
            security list-keychains -d user -s login.keychain
  
  Build-Mac-App-Production:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/production' || github.ref == 'refs/heads/production-mac' || github.ref == 'refs/heads/production-cq-mac'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: brew install python-setuptools
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0
      - run: yarn
      - run: node preBuild.js --NODE_ENV production
      - name: Setup KeyChain
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
          # default again user login keychain
          security list-keychains -d user -s login.keychain

          # Create temp keychain
          security create-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Append temp keychain to the user domain
          security list-keychains -d user -s "$MY_KEYCHAIN" $(security list-keychains -d user | sed s/\"//g)

          # Remove relock timeout
          security set-keychain-settings "$MY_KEYCHAIN"

          # Unlock keychain
          security unlock-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Add certificate to keychain
          security import $CERT -k "$MY_KEYCHAIN" -P "$CERT_PASSWORD" -A -T "/usr/bin/codesign" -T "/usr/bin/productsign"

          # Enable codesigning from a non user interactive shell
          security set-key-partition-list -S apple-tool:,apple:, -s -k $MY_KEYCHAIN_PASSWORD -D "${IDENTITY_CERTIFICATE}" -t private $MY_KEYCHAIN
          
      - name: Deploy
        env:
          CSC_KEY_PASSWORD: admin@cq
          CSC_LINK: ./macOsCertification/certificate.p12
          APPLE_APP_SPECIFIC_PASSWORD: dlen-aztg-zajf-wwqu
          APPLE_ID: <EMAIL>
          APPLE_TEAM_ID: 59B2MAAW77
        run: |
          export DEBUG=*
          yarn pub --mac --universal
      - name: Clean up
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
            # Delete temporary keychain
            security delete-keychain "$MY_KEYCHAIN"

            # default again user login keychain
            security list-keychains -d user -s login.keychain


  Build-Mac-App-Production-Chitkara:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/production' || github.ref == 'refs/heads/production-mac' || github.ref == 'refs/heads/production-chitkara' || github.ref == 'refs/heads/production-chitkara-mac'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: brew install python-setuptools
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0
      - run: yarn
      - run: npx electron-rebuild .
      - run: node preBuild.js --NODE_ENV production --CHITKARA true
      - name: Setup KeyChain
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
          # default again user login keychain
          security list-keychains -d user -s login.keychain

          # Create temp keychain
          security create-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Append temp keychain to the user domain
          security list-keychains -d user -s "$MY_KEYCHAIN" $(security list-keychains -d user | sed s/\"//g)

          # Remove relock timeout
          security set-keychain-settings "$MY_KEYCHAIN"

          # Unlock keychain
          security unlock-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Add certificate to keychain
          security import $CERT -k "$MY_KEYCHAIN" -P "$CERT_PASSWORD" -A -T "/usr/bin/codesign" -T "/usr/bin/productsign"

          # Enable codesigning from a non user interactive shell
          security set-key-partition-list -S apple-tool:,apple:, -s -k $MY_KEYCHAIN_PASSWORD -D "${IDENTITY_CERTIFICATE}" -t private $MY_KEYCHAIN
      - name: Deploy
        env:
          CSC_KEY_PASSWORD: admin@cq
          CSC_LINK: ./macOsCertification/certificate.p12
          APPLE_APP_SPECIFIC_PASSWORD: dlen-aztg-zajf-wwqu
          APPLE_ID: <EMAIL>
          APPLE_TEAM_ID: 59B2MAAW77
        run: |
          export DEBUG=*
          yarn pub --mac --universal
      - name: Clean up
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
            # Delete temporary keychain
            security delete-keychain "$MY_KEYCHAIN"

            # default again user login keychain
            security list-keychains -d user -s login.keychain



  Build-Windows-App-Jitsi-Chitkara:
    if: github.ref == 'refs/heads/jitsi-window'
    runs-on: windows-latest
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event  on  ${{ github.ref }}."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0    
      - run: yarn
      - name: Setup C
        uses: rlalik/setup-cpp-compiler@master
        with:
          compiler: gcc-13.1.0
      - run: node preBuild.js --NODE_ENV production --CHITKARA true --JITSI true
      - run: |
          yarn pub --win



  Build-Mac-App-Jitsi:
    runs-on: macos-latest
    if: github.ref == 'refs/heads/jitsi-mac'
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ github.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by GitHub!"
      - run: echo "🔎 The name of your branch is ${{ github.ref }} and your repository is ${{ github.repository }}."
      - run: brew install python-setuptools
      - uses: actions/checkout@v2 
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - name: Setup Yarn
        uses: mskelton/setup-yarn@v1.6.0
      - run: yarn
      - run: node preBuild.js --NODE_ENV production --CHITKARA true --JITSI true
      - name: Setup KeyChain
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
          # default again user login keychain
          security list-keychains -d user -s login.keychain

          # Create temp keychain
          security create-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Append temp keychain to the user domain
          security list-keychains -d user -s "$MY_KEYCHAIN" $(security list-keychains -d user | sed s/\"//g)

          # Remove relock timeout
          security set-keychain-settings "$MY_KEYCHAIN"

          # Unlock keychain
          security unlock-keychain -p "$MY_KEYCHAIN_PASSWORD" "$MY_KEYCHAIN"

          # Add certificate to keychain
          security import $CERT -k "$MY_KEYCHAIN" -P "$CERT_PASSWORD" -A -T "/usr/bin/codesign" -T "/usr/bin/productsign"

          # Enable codesigning from a non user interactive shell
          security set-key-partition-list -S apple-tool:,apple:, -s -k $MY_KEYCHAIN_PASSWORD -D "${IDENTITY_CERTIFICATE}" -t private $MY_KEYCHAIN
      - name: Deploy
        env:
          CSC_KEY_PASSWORD: admin@cq
          CSC_LINK: ./macOsCertification/certificate.p12
          APPLE_APP_SPECIFIC_PASSWORD: dlen-aztg-zajf-wwqu
          APPLE_ID: <EMAIL>
          APPLE_TEAM_ID: 59B2MAAW77
        run: |
          export DEBUG=*
          yarn pub --mac --universal
      - name: Clean up
        env: 
          MY_KEYCHAIN: "tmp-keychain"
          MY_KEYCHAIN_PASSWORD: "temp1234"
          CERT: "./macOsCertification/certificate.p12"
          CERT_PASSWORD: "admin@cq"
          IDENTITY_CERTIFICATE: "test"
        run: |
            # Delete temporary keychain
            security delete-keychain "$MY_KEYCHAIN"

            # default again user login keychain
            security list-keychains -d user -s login.keychain
