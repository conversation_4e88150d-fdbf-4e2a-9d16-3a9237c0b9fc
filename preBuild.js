const path = require('path');
const fs = require('fs');
const {spawnSync} = require('child_process');
const os = require('os');
const jsonFile = require('./package.json');
const templateFile = require('./template.json');

// SETUP ENV FOR APP
const mapOrArgs = new Map();

for(let index = 2;index < process.argv.length; index+=2) {
    const key = process.argv[index].replace('--','');
    const value = process.argv[index+1];
    if (!value) throw new Error('Invalid args passed');
    mapOrArgs.set(key, value);
}

let envString = 'module.exports = {';
mapOrArgs.forEach((value, key) => {
    envString += `\n\t${key}: "${value}",`
})
envString += '\n}';
console.log(`ENV TO USED`, envString);
fs.writeFileSync(path.join(__dirname, 'src/env.js'), envString)

if (!mapOrArgs.get('NODE_ENV')) {
    throw new Error('NODE_ENV not found please provide NODE_ENV as args');
}

// Compile CPP CODE IF ENVIRONMENT IS WINDOWS AND DELETE C PROGRAM
if (0 && os.platform() === 'win32') {
    const pathOfMonitorDetectorExe = path.join(__dirname, './extra/monitorDetect.c')
    if (os.platform() === 'win32') {
        spawnSync('gcc', [pathOfMonitorDetectorExe, '-o', path.join(pathOfMonitorDetectorExe,'../monitorDetect')]);
    }
    fs.rmSync(pathOfMonitorDetectorExe);
}

const isChitkara = mapOrArgs.get('CHITKARA') === 'true';
``
/**
 * @type {[string]}
 */
const filesToRemove = [];
/**
 * @type {[{target: string, dest: string}]}
 */
const filesToCopy = []
const publicFolderPath = path.join(__dirname, './public');
// if (isChitkara) {
//     filesToCopy.push({
//         target: path.join(__dirname, 'build/chitkara/512x512.png'),
//         dest: path.join(__dirname, 'build/icons/512x512.png')});
//     filesToCopy.push({
//         target: path.join(__dirname, 'build/chitkara/256x256.png'),
//         dest: path.join(__dirname, 'build/icons/256x256.png')});
//     filesToCopy.push({
//         target: path.join(__dirname, 'build/chitkara/installer.nsh'),
//         dest: path.join(__dirname, 'build/installer.nsh')});
//     filesToCopy.push({
//         target: path.join(publicFolderPath, '/images/chitkara.png'),
//         dest: path.join(publicFolderPath, '/images/logo.png'),
//     })
//     filesToCopy.push({
//         target: path.join(publicFolderPath, '/images/chitkara.png'),
//         dest: path.join(publicFolderPath, '/images/logo-padded.png'),
//     })
//     filesToCopy.push({
//         target: path.join(publicFolderPath, '/html/chitkara.html'),
//         dest: path.join(publicFolderPath, '/html/index.html'),
//     })
//     filesToCopy.push({
//         target: path.join(publicFolderPath, '/images/chitkara_logo_primary.png'),
//         dest: path.join(publicFolderPath, '/images/cq-logo.svg'),
//     })
// } else {
//     filesToRemove.push(path.join(publicFolderPath, '/images/chitkara.png'));
//     filesToRemove.push(path.join(publicFolderPath, '/html/chitkara.html'));
//     filesToRemove.push(path.join(__dirname, 'build/chitkara'));
// }

filesToCopy.forEach(({target, dest}) => {
    fs.copyFileSync(target, dest);
})

filesToRemove.forEach(element => {
    const state = fs.statSync(element);
    if (state.isDirectory()) {
        return fs.rmdirSync(element, { recursive: true, force: true })
    }
    return fs.rmSync(element)
});

//Setup Publisher
switch(mapOrArgs.get('NODE_ENV')) {
    case 'testing': {
        templateFile.publish.path = "/Testing/TestApp";
        templateFile.asar = true;
        break;
    }
    case 'production': {
        templateFile.asar = true;
        templateFile.publish.path = "/Production/TestApp";
        break;
    }
    default: {
        throw new Error("Invalid Environment")
    }
}

if (os.platform() === 'darwin') {
    if (isChitkara) {
        fs.cpSync(path.join(__dirname, 'macOsCertification/testpad.p12'), path.join(__dirname, 'macOsCertification/certificate.p12'));
    } else {
        fs.cpSync(path.join(__dirname, 'macOsCertification/test_codequotient.p12'), path.join(__dirname, "macOsCertification/certificate.p12"));
    }
}

// UPDATE Publish Path
if (isChitkara) {
    fs.rmSync(path.join(__dirname, 'electron-builder.env'));
    fs.cpSync(path.join(__dirname, 'electron-builder.chitkara.env'), path.join(__dirname, 'electron-builder.env'));

    templateFile.publish.name = "chitkara-testpad-cdn"
    templateFile.publish.path = templateFile.publish.path;
    templateFile.appId = "com.chitkara.testpad";
    templateFile.productName = "Testpad";
    templateFile.protocols.name = "TestPad Chitkara";
    templateFile.protocols.schemes = ["testpad-chitkara"];
    templateFile.linux.desktop.MimeType = "x-scheme-handler/testpad-chitkara";
}

// Update package.json
if (isChitkara) {
    jsonFile.name = 'testpad';
    jsonFile.author.email = '<EMAIL>';
    jsonFile.author.name = 'Chitkara';
    fs.writeFileSync(path.join(__dirname, '/package.json'), JSON.stringify(jsonFile));
}

let url = `https://${templateFile.publish.name}.${templateFile.publish.region}.digitaloceanspaces.com${templateFile.publish.path}`;
switch(os.platform()) {
    case 'win32': {
        url += '/latest.yml';
        break;
    }
    case 'darwin': {
        url += '/latest-mac.yml';
        break;
    }
    case 'linux': {
        url += '/latest-linux.yml';
        break;
    }
}
fetch(url).then(async (data) => {
    data = await data.text();
    if (!data.startsWith('version')) {
        throw new Error('Something went wrong: ', data);
    }
    let version = data.split('\n')[0].replace('version:','').trim();
    if (version === jsonFile.version) {
        throw new Error('Version Are Same Please Update Version');
    }
    console.log(`Using Version: `, jsonFile.version);
}).catch((error) => {
    console.log(error);
})

fs.writeFileSync(path.join(__dirname, '/package.json'), JSON.stringify(jsonFile));
fs.writeFileSync(path.join(__dirname, './electron-builder.json'), JSON.stringify(templateFile));