const fs = require('fs');
const path = require('path');
const bytenode = require('bytenode');

// Directories to compile
const sourceDirectories = ['src', 'public/preload'];
const outputDirectory = 'bytecode-build';

// Files to exclude from compilation
const excludeFiles = [
    'src/env.js', // Generated file
    'node_modules',
    '.git',
    'dist',
    'build'
];

// File extensions to compile
const compileExtensions = ['.js', '.mjs'];

/**
 * Check if a file should be excluded from compilation
 */
function shouldExclude(filePath) {
    return excludeFiles.some(exclude => filePath.includes(exclude));
}

/**
 * Check if a file contains ES6 import statements
 */
function hasESModuleImports(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        // Check for ES6 import statements
        return /^\s*import\s+/m.test(content) || /^\s*export\s+/m.test(content);
    } catch (error) {
        return false;
    }
}

/**
 * Check if a file should be compiled
 */
function shouldCompile(filePath) {
    const ext = path.extname(filePath);
    if (!compileExtensions.includes(ext) || shouldExclude(filePath)) {
        return false;
    }

    // Skip ES modules as bytenode doesn't support them
    if (hasESModuleImports(filePath)) {
        console.log(`Skipping ES module: ${filePath} (contains import/export statements)`);
        return false;
    }

    return true;
}

/**
 * Recursively get all files in a directory
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
        } else {
            arrayOfFiles.push(fullPath);
        }
    });

    return arrayOfFiles;
}

/**
 * Create directory structure if it doesn't exist
 */
function ensureDirectoryExists(filePath) {
    const dirname = path.dirname(filePath);
    if (!fs.existsSync(dirname)) {
        fs.mkdirSync(dirname, { recursive: true });
    }
}

/**
 * Update require statements to work with bytecode files
 */
function updateRequireStatements(sourceCode) {
    // Update relative requires that end with .js to remove the extension
    // This allows Node.js to find the .jsc files
    const updatedCode = sourceCode.replace(
        /require\(['"`](\.[^'"`]*?)\.js['"`]\)/g,
        "require('$1')"
    );

    return updatedCode;
}

/**
 * Compile a single JavaScript file to bytecode
 */
function compileFile(inputPath, outputPath) {
    try {
        console.log(`Compiling: ${inputPath} -> ${outputPath}`);

        // Read the source file
        let sourceCode = fs.readFileSync(inputPath, 'utf8');

        // Update require statements for bytecode compatibility
        sourceCode = updateRequireStatements(sourceCode);

        // Create a temporary file with updated requires
        const tempPath = inputPath + '.temp';
        fs.writeFileSync(tempPath, sourceCode);

        // Ensure output directory exists
        ensureDirectoryExists(outputPath);

        // Compile to bytecode
        bytenode.compileFile(tempPath, outputPath);

        // Clean up temporary file
        fs.unlinkSync(tempPath);

        console.log(`✓ Successfully compiled: ${inputPath}`);
        return true;
    } catch (error) {
        console.error(`✗ Failed to compile ${inputPath}:`, error.message);
        // Clean up temporary file if it exists
        const tempPath = inputPath + '.temp';
        if (fs.existsSync(tempPath)) {
            fs.unlinkSync(tempPath);
        }
        return false;
    }
}

/**
 * Copy non-JS files to output directory
 */
function copyFile(inputPath, outputPath) {
    try {
        ensureDirectoryExists(outputPath);
        fs.copyFileSync(inputPath, outputPath);
        console.log(`Copied: ${inputPath} -> ${outputPath}`);
        return true;
    } catch (error) {
        console.error(`Failed to copy ${inputPath}:`, error.message);
        return false;
    }
}

/**
 * Create a loader file that requires bytenode and loads the compiled main file
 */
function createMainLoader() {
    const loaderContent = `// Bytecode loader for secure build
require('bytenode');
require('./src/index.jsc');
`;
    
    const loaderPath = path.join(outputDirectory, 'index.js');
    fs.writeFileSync(loaderPath, loaderContent);
    console.log(`Created main loader: ${loaderPath}`);
}

/**
 * Main compilation function
 */
function compileProject() {
    console.log('Starting bytecode compilation...');
    
    // Clean output directory
    if (fs.existsSync(outputDirectory)) {
        fs.rmSync(outputDirectory, { recursive: true, force: true });
    }
    fs.mkdirSync(outputDirectory, { recursive: true });
    
    let compiledCount = 0;
    let copiedCount = 0;
    let errorCount = 0;
    
    // Process each source directory
    sourceDirectories.forEach(sourceDir => {
        if (!fs.existsSync(sourceDir)) {
            console.warn(`Source directory not found: ${sourceDir}`);
            return;
        }
        
        const allFiles = getAllFiles(sourceDir);
        
        allFiles.forEach(filePath => {
            const relativePath = path.relative('.', filePath);
            const outputPath = path.join(outputDirectory, relativePath);
            
            if (shouldCompile(filePath)) {
                // Compile JS files to bytecode
                const bytecodeOutputPath = outputPath.replace(/\.(js|mjs)$/, '.jsc');
                if (compileFile(filePath, bytecodeOutputPath)) {
                    compiledCount++;
                } else {
                    errorCount++;
                }
            } else if (!shouldExclude(filePath)) {
                // Copy other files as-is
                if (copyFile(filePath, outputPath)) {
                    copiedCount++;
                } else {
                    errorCount++;
                }
            }
        });
    });
    
    // Copy other necessary directories/files
    const otherDirectories = ['public/css', 'public/html', 'public/images', 'public/js', 'extra'];
    otherDirectories.forEach(dir => {
        if (fs.existsSync(dir)) {
            const allFiles = getAllFiles(dir);
            allFiles.forEach(filePath => {
                const relativePath = path.relative('.', filePath);
                const outputPath = path.join(outputDirectory, relativePath);
                
                if (shouldCompile(filePath)) {
                    const bytecodeOutputPath = outputPath.replace(/\.(js|mjs)$/, '.jsc');
                    if (compileFile(filePath, bytecodeOutputPath)) {
                        compiledCount++;
                    } else {
                        errorCount++;
                    }
                } else {
                    if (copyFile(filePath, outputPath)) {
                        copiedCount++;
                    } else {
                        errorCount++;
                    }
                }
            });
        }
    });
    
    // Copy package.json and other necessary files
    const necessaryFiles = ['package.json', 'package-lock.json'];
    necessaryFiles.forEach(file => {
        if (fs.existsSync(file)) {
            const outputPath = path.join(outputDirectory, file);
            if (copyFile(file, outputPath)) {
                copiedCount++;
            } else {
                errorCount++;
            }
        }
    });
    
    // Create main loader
    createMainLoader();
    
    console.log('\n=== Compilation Summary ===');
    console.log(`Files compiled to bytecode: ${compiledCount}`);
    console.log(`Files copied: ${copiedCount}`);
    console.log(`Errors: ${errorCount}`);
    
    if (errorCount === 0) {
        console.log('✓ Bytecode compilation completed successfully!');
        console.log(`Output directory: ${outputDirectory}`);
    } else {
        console.log('⚠ Compilation completed with errors.');
        process.exit(1);
    }
}

// Run compilation
if (require.main === module) {
    compileProject();
}

module.exports = { compileProject };
